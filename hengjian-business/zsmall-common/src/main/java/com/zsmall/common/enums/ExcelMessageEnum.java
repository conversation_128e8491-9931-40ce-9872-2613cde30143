package com.zsmall.common.enums;

import cn.hutool.core.util.StrUtil;
import com.hengjian.common.core.utils.MessageUtils;
import com.zsmall.common.domain.LocaleMessage;

import java.util.Locale;

/**
 * Excel上传信息枚举
 *
 * <AUTHOR>
 * @create 2022/6/6 11:52
 */
public enum ExcelMessageEnum {

    NOT_VALID_ROW("zsmall.excelImport.notValidRow"),
    MESSAGE_HEAD("zsmall.excelImport.messageHead"),
    MESSAGE_HEAD_NO_COLUMN("zsmall.excelImport.messageHeadNoColumn"),
    UNKNOWN_PARSING_ERROR("zsmall.excelImport.unknownParsingError"),

    REQUIRE("zsmall.excelImport.required"),
    COUNTRY_CODE_NOT_EXIST("zsmall.excelImport.countryCodeNotExist"),

    ILLEGAL_ARGUMENT("zsmall.excelImport.illegalArgument"),
    INTEGER_GTE_ZERO("zsmall.excelImport.integerGteZero"),
    ILLEGAL_DROP_SHIPPING_QUANTITY("zsmall.excelImport.illegalDropShippingQuantity"),

    /* 订单导入相关 */
    SKU_NOT_EXIST("zsmall.excelImport.skuNotExist"),

    /** 仓库id不存在 */
    WAREHOUSE_SYSTEM_CODE_NOT_EXIST("zsmall.excelImport.warehouseSystemCode"),
    /** 承运商不存在 */
    LOGISTICS_CARRIER_NOT_EXIST("zsmall.excelImport.logisticsCarrier"),
    CHANNEL_NOT_EXIST("zsmall.excelImport.channelNotExist"),
    CHANNEL_ORDER_NUMBER_CANNOT_BE_EMPTY("zsmall.excelImport.channelOrderNumberCannotBeEmpty"),
    /** 运单号不存在 */
    LOGISTICS_TRACKINGNO_NOT_EXIST("zsmall.excelImport.logisticsTrackingNo"),
    IRREGULARITY("zsmall.excelImport.irregularity"),
    COUNTRY("zsmall.excelImport.country"),
    STATE("zsmall.excelImport.state"),
    CARRIER_CODE("zsmall.excelImport.carrierCode"),
    TRACKING_FORMAT("zsmall.excelImport.trackingFormat"),
    ZIP_CODE("zsmall.excelImport.zipCode"),
    SHIPPING_TYPE("zsmall.excelImport.shippingType"),
    DROP_SHIPPING_LIMIT("zsmall.excelImport.dropShippingLimit"),
    PICK_UP_CARRIER("zsmall.excelImport.pickUpCarrier"),
    THIRD_BILLING_TRACKING_NO("zsmall.excelImport.thirdBillingTrackingNo"),
    PICK_UP_THIRD_BILLING("zsmall.excelImport.pickUpThirdBilling"),
    THIRD_BILLING_CA("zsmall.excelImport.thirdBillingCA"),
    THIRD_BILLING_CA_ZIP_CODE("zsmall.excelImport.thirdBillingCAZipCode"),
    PHONE_NUMBER_LENGTH("zsmall.excelImport.phoneNumberLength"),
    NOT_THIRD_BILLING_TRACKING_NO("zsmall.excelImport.notThirdBillingTrackingNo"),
    NOT_THIRD_BILLING_CA("zsmall.excelImport.notThirdBillingCA"),
    NOT_THIRD_BILLING_CA_ZIP_CODE("zsmall.excelImport.notThirdBillingCAZipCode"),
    THIRD_BILLING_NOT_SUPPORT_WAREHOUSE_CODE("zsmall.excelImport.thirdBillingNotSupportWarehouseCode"),
    REPEAT_STORE_ORDER_ID("zsmall.excelImport.repeatStoreOrderID"),
    ITEM_NO_NOT_EXIST("zsmall.excelImport.itemNoNotExist"),
    DROP_SHIPPING_ONLY("zsmall.excelImport.dropShippingOnly"),
    PICK_UP_ONLY("zsmall.excelImport.pickUpOnly"),
    ACTIVITY_NOT_EXIST("zsmall.excelImport.activityNotExist"),
    UNRECOGNIZABLE_TRACKING_NO("zsmall.excelImport.unrecognizableTrackingNo"),
    ITEM_NO_NOT_JOIN_ACTIVITY("zsmall.excelImport.itemNoNotJoinActivity"),
    PRICE_RULE_NO_EXIST("zsmall.excelImport.priceRuleNoExist"),

    /**
     * 库存导入相关
     */
    /** SKU不存在于指定仓库 */
    PRODUCT_SKU_NOT_IN_WAREHOUSE("zsmall.excelImport.productSkuNotInWarehouse"),

    /* 商品导入相关 */
    /** 商品名称超过限制长度 */
    PRODUCT_NAME_LIMIT("zsmall.excelImport.productNameLimit"),
    /** 商品分类未找到 */
    PRODUCT_CATEGORY_NOT_FOUND("zsmall.excelImport.productCategoryNotFound"),
    /** 商品变体维度未找到或不属于当前分类 */
    PRODUCT_VARIANT_DIMENSION_NOT_FOUND("zsmall.excelImport.productVariantDimensionNotFound"),
    /** 填写的变体维度不支持自定义值 */
    PRODUCT_VARIANT_NOT_SUPPORT_CUSTOM("zsmall.excelImport.productVariantNotSupportCustom"),
    /** 变体值重复 */
    PRODUCT_VARIANT_VALUE_REPEAT("zsmall.excelImport.productVariantValueRepeat"),
    /** 重复的变体维度 */
    DUPLICATE_VARIANT("zsmall.excelImport.duplicateVariant"),
    /**
     * 重复的变体值组合
     */
    DUPLICATE_VARIANT_VALUE_COMBINATION("zsmall.excelImport.duplicateVariantValueCombination"),
    /** 仓库未找到 */
    WAREHOUSE_NOT_FOUND("zsmall.excelImport.warehouseNotFound"),
    /** 物流模板未找到或未关联仓库 */
    LOGISTICS_TEMPLATE_NOT_FOUND("zsmall.excelImport.logisticsTemplateNotFound"),
    /** 仓库的类型必须相同 */
    WAREHOUSE_TYPE_MUST_BE_SAME("zsmall.excelImport.warehouseTypeMustBeSame"),
    /**
     * 无效的图片
     */
    INVALID_IMAGE("zsmall.excelImport.invalidImage"),
    /**
     * 价格不能小于零
     */
    PRICE_CANNOT_BE_LESS_THAN_ZERO("zsmall.excelImport.priceCannotBeLessThanZero"),
    /**
     * SKU重复
     */
    SKU_REPEAT("zsmall.excelImport.skuRepeat"),
    /**
     * UPC重复
     */
    UPC_REPEAT("zsmall.excelImport.upcRepeat"),

    /* 批量发货相关 */
    NO_PRODUCT_DISPATCHED("zsmall.excelImport.noProductDispatched"),
    CONFIRM_QUANTITY_GT_UNDISPATCHED("zsmall.excelImport.confirmQuantityGtUndispatched"),
    ORDER_NOT_EXIST("zsmall.orders.orderNotExist"),
    ORDER_HAS_NOT_BEEN_PAID("zsmall.orders.orderHasNotBeenPaid"),
    ORDER_REFUNDING_CANT_FULFILL("zsmall.orders.orderRefundingCantFulfill"),

    /* 商品价格导入相关 */
    PRODUCT_NOT_EXIST("zsmall.product.productNotExist"),
    ITEM_NO_REPEAT("zsmall.excelImport.itemNoRepeat"),

    PRODUCT_SITE_NOT_EXIST("zsmall.product.productSiteNotExist"),

    PRICE_GREATER_ZERO("zsmall.excelImport.priceGreaterZero"),
    PRODUCT_PRICE_NOT_CHANGE("zsmall.excelImport.productPriceNotChange"),
    PRODUCT_PRICE_FORMAT_ERROR("zsmall.excelImport.productPriceFormatError"),
    NEW_PRODUCT_PRICE_VERIFY_PENDING("zsmall.product.newProductPriceVerifyPending"),

    /* 国内现货导入相关 */
    CHINA_SPOT_REQUIRED("zsmall.excelImport.spot.required"),
    CHINA_SPOT_PRODUCT_NAME_CHAR_LIMIT("zsmall.excelimport.spot.productNameCharLimit"),
    CHINA_SPOT_CATEGORY_NOT_EXIST("zsmall.excelImport.spot.categoryNotExist"),
    CHINA_SPOT_QUANTITY_ERROR("zsmall.excelimport.spot.quantityError"),
    CHINA_SPOT_SKU_REPEAT("zsmall.excelImport.spot.skuRepeat"),
    CHINA_SPOT_ONLY_ENGLISH("zsmall.excelImport.spot.onlyEnglish"),
    CHINA_SPOT_INVALID_IMAGE_LINK("zsmall.excelImport.spot.invalidImageLink"),
    CHINA_SPOT_SKU_SIZE_ERROR("zsmall.excelImport.spot.skuSizeError"),





    /* 批量商品映射相关 */
    /**
     * 渠道店铺未找到
     * Channel store not found
     */
    CHANNEL_STORE_NOT_FOUND("zsmall.excelImport.channelStoreNotFound"),
    /**
     * 同一个渠道店铺不能存在重复的映射Sku
     * Duplicate mapping sku are not allowed in the same store!
     */
    DUPLICATE_MAPPING_SKU("zsmall.excelImport.duplicateMappingSku"),
    /**
     * Item No.不存在，无法映射
     * Item No. does not exist and cannot be mapped
     */
    ITEM_NO_DOES_NOT_EXIST("zsmall.excelImport.itemNoDoesNotExist"),

    /**
     * 无效承运商
     */
    NON_VALID_CARRIER("zsmall.excelImport.nonValidCarrier"),
    CARRIER_CONFIGURATION_ABNORMAL("zsmall.excelImport.carrierConfigurationAbnormal"),
//    walmart渠道不支持amsp
    WALMART_CHANNEL_NOT_SUPPORT_AMSP("zsmall.excelImport.walmartChannelNotSupportAmsp"),
    BEST_BUY_CHANNEL_ONLY_SUPPORT_FEDEX("zsmall.excelImport.bestBuyChannelOnlySupportFedex"),

    MULTIPLE_CURRENCIES_ARE_NOT_SUPPORTED("zsmall.excelImport.multipleCurrenciesAreNotSupported"),
    SKU_ID_PRICE_SITE_PRICE_NOT_EXIST("zsmall.excelImport.skuIdPriceSitePriceNotExist"),
    TRACKING_CANNOT_BE_EMPTY("zsmall.excelImport.trackingCannotBeEmpty"),
    ORDER_NO_CANNOT_BE_EMPTY("zsmall.excelImport.orderNoCannotBeEmpty"),
    ORDER_ITEM_NOT_EXIST("zsmall.excelImport.orderItemNotExist"),
    ADDRESS_NOT_EXIST("zsmall.excelImport.addressNotExist"),
    LOGISTICS_NOT_EXIST("zsmall.excelImport.logisticsNotExist"),

    PRODUCT_SKU_NOT_EXIST("zsmall.excelImport.productSkuNotExist"),
    PRODUCT_SKU_CANNOT_BE_EMPTY("zsmall.excelImport.productSkuCannotBeEmpty"),
    TRACKING_TOO_LONG("zsmall.excelImport.trackingTooLong"),
    CARRIER_TOO_LONG("zsmall.excelImport.carrierTooLong"),
    CHANNEL_ORDER_NO_TOO_LONG("zsmall.excelImport.channelOrderNoTooLong"),
    TRACKING_PICK_UP_ONLY("zsmall.excelImport.trackingPickUpOnly"),;


    private String messageCode;

    private String columnName;

    private String showRow;

    private Object[] args;

    ExcelMessageEnum(String messageCode) {
        this.messageCode = messageCode;
    }

    public ExcelMessageEnum args(Object... args) {
        this.args = args;
        return this;
    }

    public String getMessageCode() {
        return messageCode;
    }

    public Object[] getArgs() {
        return args;
    }

    public String getZh_CN(Integer showRow, String columnName) {
        this.showRow = StrUtil.toString(showRow);
        this.columnName = columnName;
        return buildMessage(Locale.SIMPLIFIED_CHINESE);
    }

    public String getEn_US(Integer showRow, String columnName) {
        this.showRow = StrUtil.toString(showRow);
        this.columnName = columnName;
        return buildMessage(Locale.US);
    }

    public LocaleMessage buildLocalMessage(Integer showRow) {
        this.showRow = StrUtil.toString(showRow);
        return new LocaleMessage(buildMessage(Locale.SIMPLIFIED_CHINESE), buildMessage(Locale.US));
    }

    public LocaleMessage buildLocalMessage(Integer showRow, String columnName) {
        this.showRow = StrUtil.toString(showRow);
        this.columnName = columnName;
        return new LocaleMessage(buildMessage(Locale.SIMPLIFIED_CHINESE), buildMessage(Locale.US));
    }

    public LocaleMessage buildLocalMessage() {
        return new LocaleMessage(buildMessage(Locale.SIMPLIFIED_CHINESE), buildMessage(Locale.US));
    }

    private String buildMessage(Locale locale) {
        StringBuilder message = new StringBuilder();
        if (this.showRow != null) {
            if (StrUtil.isNotBlank(this.columnName)) {
                message.append(MessageUtils.message(locale, MESSAGE_HEAD.messageCode, this.showRow, this.columnName));
            } else {
                message.append(MessageUtils.message(locale, MESSAGE_HEAD_NO_COLUMN.messageCode, this.showRow));
            }
        }
        return message.append(MessageUtils.message(locale, this.messageCode, args)).toString();
    }


}
