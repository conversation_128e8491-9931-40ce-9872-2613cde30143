package com.zsmall.common.enums.common;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * lty notes,次类和dict_type shipping
 *
 * <AUTHOR> Theo
 * @create 2024/8/26 14:26
 */
@Getter
@AllArgsConstructor
public enum CarrierTypeEnum implements IEnum<String> {
    UPS(1),
    AMSP(2),
    FedEx(3),
    Any(4),
    LTL(5),
    Ontrac(6),
    AMXL(7),
    ;
    private Integer channelId;
    @Override
    public String getValue() {
        return this.name();
    }

    public boolean equalsIgnoreCase(String name) {
        return this.name().equalsIgnoreCase(name);
    }
}
