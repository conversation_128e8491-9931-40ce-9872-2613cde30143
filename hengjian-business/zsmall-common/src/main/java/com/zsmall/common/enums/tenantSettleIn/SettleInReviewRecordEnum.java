package com.zsmall.common.enums.tenantSettleIn;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 公司性质性质
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum SettleInReviewRecordEnum implements IEnum<String> {

    /**
     * 待审核
     */
    Reviewing,
    /**
     * 通过
     */
    Approved,
    /**
     * 驳回
     */
    Rejected,

    /**
     * 修改后待审核
     */
    ReviewedAgain,
    ;

    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
        return this.name();
    }

    public static String getDesc(SettleInReviewRecordEnum settleInReviewRecordEnum){
        if (SettleInReviewRecordEnum.Reviewing.equals(settleInReviewRecordEnum)){
            return "待审核";
        }
        if (SettleInReviewRecordEnum.Approved.equals(settleInReviewRecordEnum)){
            return "已通过";
        }
        if (SettleInReviewRecordEnum.Rejected.equals(settleInReviewRecordEnum)){
            return "已驳回";
        }
        if (SettleInReviewRecordEnum.ReviewedAgain.equals(settleInReviewRecordEnum)){
            return "修改后待审核";
        }
        return null;
    }

}
