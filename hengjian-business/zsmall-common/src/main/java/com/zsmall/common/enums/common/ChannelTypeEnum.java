package com.zsmall.common.enums.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.parser.ParserConfig;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.zsmall.common.deserializer.ChannelTypeEnumDeserializer;
import com.zsmall.common.deserializer.ChannelTypeEnumJacksonDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 渠道类型枚举
 *
 * <AUTHOR>
 * @date 2023/5/30
 */
@Getter
@AllArgsConstructor
@JsonDeserialize(using = ChannelTypeEnumJacksonDeserializer.class)
public enum ChannelTypeEnum implements IEnum<String> {

    /**
     * Amazon
     */
    Amazon(1),

    /**
     * Shopify
     */
    Shopify(2),

    /**
     * Wayfair
     */
    Wayfair(3),

    /**
     * 其他渠道
     */
    Others(4),

    /**
     * OneLink
     */
    OneLink(5),

    /**
     * 自营商城
     */
    Marketplace(6),

    /**
     * 乐天市场
     */
    Rakuten(7),
    /**
     * 多渠道
     */
    MultiChannel(8),
    /**
     * erp
     */
    Erp(9),
    Shein(10),
    TikTok(11),
    Walmart(12),
    SC(13),
    OverStock(14),
    Ebay(15),
    Walmart_dsv(16),
    Microsoft(17),
    HomeDepot(18),
    Temu(19),
    AliExpress(20),
    VC_DF(21),
    VC_DI(22),
    VC_USPO(23),
    Open(24),
    Amazon_SC(25),
    Amazon_VC(26),
    Target(27),
    EC(28),
//    新增
    Tomo(29),
    Woot(30),
    Shein_full(31),
    Shein_semi(32),
    Officedepot(33),
    Offline(34),
    Hsn(335),
    Costco(36),
    BestBuy(37),
    B2b(38),
    B2c(39),
    Alibaba_com(40),
    Sweet_Furniture(41),
    ;
    private Integer channelId;
    // vc-df
    /**
     * 枚举数据库存储值
     */
    @Override
    public String getValue() {
//        String name = this.name();
//        if(name.contains("_")){
//            return name.replace("_","-");
//        }
        return this.name();
    }

    /**
     * 存在定时任务业务的渠道类型
     * @return
     */
    public static List<ChannelTypeEnum> jobBusiness() {
        return CollUtil.newArrayList(Shopify, Wayfair, Rakuten);
    }

    public static List<Integer> convertChannelId(List<String> channelList) {
        return channelList.stream().map(channel -> ChannelTypeEnum.valueOf(channel).channelId).collect(Collectors.toList());
    }

    public static ChannelTypeEnum getChannelTypeEnum(String name) {
        if(ObjectUtil.isNotEmpty(name)){
            for (ChannelTypeEnum value : ChannelTypeEnum.values()) {
                if(value.name().equalsIgnoreCase(name)){
                    return value;
                }
            }
        }
        return null;
    }

    public static ChannelTypeEnum getChannelTypeEnumV2(String name) {
        if(ObjectUtil.isNotEmpty(name)){
            for (ChannelTypeEnum value : ChannelTypeEnum.values()) {
                if(value.name().equalsIgnoreCase(name)){
                    return value;
                }
            }
        }
        return null;
    }
    public boolean equalsIgnoreCase(String name) {
        return this.name().equalsIgnoreCase(name);
    }
    static {
        ParserConfig.getGlobalInstance()
                    .putDeserializer(ChannelTypeEnum.class, new ChannelTypeEnumDeserializer());
    }
}
