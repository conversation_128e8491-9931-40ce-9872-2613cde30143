package com.zsmall.system.entity.domain.bo.settleInBasic;

import com.zsmall.system.entity.domain.vo.settleInBasic.ContactBo;
import com.zsmall.system.entity.domain.vo.settleInBasic.SupSettleInBasicBo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 请求参数-供应商入驻信息
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SupSettleInInfoBo {

    /**
     * 是否提交审核
     */
    private Boolean isSubmit;

    /**
     * 审查状态
     */
    private String reviewStatus;
    /**
     * 商品源类型
     */
    private String productSourceType;
    /**
     * 基础信息
     */
    private SupSettleInBasicBo basicBody;
    /**
     * 联系人信息
     */
    private ContactBo contactBody;
    /**
     * 扩展信息
     */
    private SupSettleInExtendVo extendedBody;

}
