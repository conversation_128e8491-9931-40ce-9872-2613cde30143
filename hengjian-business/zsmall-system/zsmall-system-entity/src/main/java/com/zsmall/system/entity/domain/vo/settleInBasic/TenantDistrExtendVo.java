package com.zsmall.system.entity.domain.vo.settleInBasic;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.hengjian.common.excel.annotation.ExcelDictFormat;
import com.hengjian.common.excel.convert.ExcelDictConvert;
import com.zsmall.system.entity.domain.TenantDistrExtend;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 租户分销商-拓展信息视图对象 tenant_distr_extend
 *
 * <AUTHOR> Li
 * @date 2023-05-29
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = TenantDistrExtend.class)
public class TenantDistrExtendVo implements Serializable {


    private static final long serialVersionUID = 1L;


    private Long id;

    /**
     * 是否存在公司主体（0-无，1-有）
     */
    @ExcelProperty(value = "是否存在公司主体", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "distributor_has_company")
    private Boolean hasCompany;

    /**
     * 主营类目（一级分类id，用逗号分隔）
     */
    @ExcelProperty(value = "主营类目", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "一=级分类id，用逗号分隔")
    private String mainCategories;
    /**
     * 主营类目文本
     */
    private String mainCategoriesName;

    /**
     * 团队规模
     */
    @ExcelProperty(value = "团队规模")
    private String teamSize;

    /**
     * 公司所在国家id
     */
    @ExcelProperty(value = "公司所在国家id")
    private Long companyCountryId;

    /**
     * 公司所在国家（非中美手动填写）
     */
    @ExcelProperty(value = "公司所在国家")
    private String companyCountryText;

    /**
     * 公司所在国家文本
     */
    private JSONObject companyCountryName;

    /**
     * 公司所在州/省id
     */
    @ExcelProperty(value = "公司所在州/省id")
    private Long companyStateId;

    /**
     * 公司所在州/省文本
     */
    private JSONObject companyStateName;

    /**
     * 公司所在州/省（非中美手动填写）
     */
    @ExcelProperty(value = "公司所在州/省", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "非=中美手动填写")
    private String companyStateText;

    /**
     * 公司所在市id
     */
//    @ExcelProperty(value = "公司所在市id")
//    private Long companyCityId;

    /**
     * 公司所在市（非中美手动填写）
     */
    @ExcelProperty(value = "公司所在市", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "非=中美手动填写")
    private String companyCityText;

    /**
     * 公司联系地址
     */
    @ExcelProperty(value = "公司联系地址")
    private String companyContactAddress;

    /**
     * 近一年年销售规模（万）
     */
    @ExcelProperty(value = "近一年年销售规模", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "万=")
    private String recentAnnualSalesScale;

    /**
     * 跨境电商经验
     */
    @ExcelProperty(value = "跨境电商经验", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "distributor_cec_experience")
    private String cecExperience;
    /**
     * 跨境电商经验
     */
    private String cecExperienceText;

    /**
     * 其他线上销售渠道经验（逗号隔开）
     */
    @ExcelProperty(value = "其他线上销售渠道经验", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "distributor_other_experience")
    private String otherExperience;
    /**
     * 其他线上销售渠道经验
     */
    private List<String> otherExperienceArr;

    /**
     * 其他渠道销售经验
     */
    private String otherChannelExperience;

    /**
     * 公司税务等级号
     */
    private String companyTaxRegistrationNumber;

    /**
     * 租户编号
     */
    private String tenantId;

    private String companyName;

    private String contactEmail;

    private String contactPhone;

    private String contactUserName;

    private String messagingAppNumber;

    private String messagingAppType;

    private String reviewStatus;

}
