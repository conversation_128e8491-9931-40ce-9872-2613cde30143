package com.hengjian.system.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Console;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hengjian.common.core.constant.CacheNames;
import com.hengjian.common.core.constant.Constants;
import com.hengjian.common.core.constant.TenantConstants;
import com.hengjian.common.core.domain.R;
import com.hengjian.common.core.enums.TenantType;
import com.hengjian.common.core.exception.ServiceException;
import com.hengjian.common.core.utils.*;
import com.hengjian.common.excel.utils.ExcelUtil;
import com.hengjian.common.mybatis.core.page.PageQuery;
import com.hengjian.common.mybatis.core.page.TableDataInfo;
import com.hengjian.common.satoken.utils.LoginHelper;
import com.hengjian.common.tenant.core.TenantEntity;
import com.hengjian.common.tenant.helper.TenantHelper;
import com.hengjian.system.constant.SysConstant;
import com.hengjian.system.domain.*;
import com.hengjian.system.domain.bo.SysTenantBo;
import com.hengjian.system.domain.vo.ApprovedTenantVo;
import com.hengjian.system.domain.vo.SysTenantExportVo;
import com.hengjian.system.domain.vo.SysTenantVo;
import com.hengjian.system.domain.vo.SysUserVo;
import com.hengjian.system.enums.SysConstantEnum;
import com.hengjian.system.mapper.*;
import com.hengjian.system.service.ISysConfigService;
import com.hengjian.system.service.ISysTenantService;
import com.zsmall.common.constant.FileNameConstants;
import com.zsmall.common.enums.downloadRecord.DownloadTypePlusEnum;
import com.zsmall.system.entity.util.DownloadRecordUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 租户Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SysTenantServiceImpl implements ISysTenantService {

    private final SysTenantMapper baseMapper;
    private final SysTenantPackageMapper tenantPackageMapper;
    private final SysUserMapper userMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysRoleMenuMapper roleMenuMapper;
    private final SysRoleDeptMapper roleDeptMapper;
    private final SysUserRoleMapper userRoleMapper;
    //    private final SysDictTypeMapper dictTypeMapper;
//    private final SysDictDataMapper dictDataMapper;
//    private final SysConfigMapper configMapper;
    private final ISysConfigService iSysConfigService;
    private final SysCodeGenerator sysCodeGenerator;

    /**
     * 查询租户
     */
    @Override
    public SysTenantVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 基于租户ID查询租户
     */
    @Override
    public SysTenantVo queryByTenantId(String tenantId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysTenant>().eq(SysTenant::getTenantId, tenantId));
    }

    /**
     * 基于租户ID查询租户无视租户
     *
     * @param tenantId
     */
    @Override
    public SysTenantVo queryByTenantIdNoTenant(String tenantId) {
        return TenantHelper.ignore(() -> baseMapper.selectVoOne(new LambdaQueryWrapper<SysTenant>().eq(SysTenant::getTenantId, tenantId)));
    }

    @Override
    public SysTenant queryOneByTenantId(String tenantId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysTenant>().eq(SysTenant::getTenantId, tenantId));
    }

    @Override
    public List<SysTenant> queryListByTenantIds(List<String> tenantIds) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysTenant>().in(SysTenant::getTenantId, tenantIds));
    }

    /**
     * 查询租户列表
     */
    @Override
    public TableDataInfo<SysTenantVo> queryPageList(SysTenantBo bo, PageQuery pageQuery) {
        String queryValue = bo.getQueryValue();

        if(StrUtil.isEmpty(queryValue)){
            bo.setQueryType(null);
        }
        Page<SysTenantVo> result = getSysTenantVoPage(bo, pageQuery);
        nullValueProcessing(result);
        List<SysTenantVo> records = result.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            for (SysTenantVo tenantVo : records) {
                String tenantId = tenantVo.getTenantId();
                if (StrUtil.equals(tenantId, TenantConstants.DEFAULT_TENANT_ID)) {
                    continue;
                }

                List<SysUserVo> sysUserVos = userMapper.selectTenantUserByRoleAdmin(tenantId);
                if (CollUtil.isNotEmpty(sysUserVos)) {
                    SysUserVo sysUserVo = sysUserVos.get(0);
                    tenantVo.setSysUser(sysUserVo);
                }
            }
        }

        return TableDataInfo.build(result);
    }

    private Page<SysTenantVo> getSysTenantVoPage(SysTenantBo bo, PageQuery pageQuery) {
        Page<SysTenantVo> result = baseMapper.queryPage(bo, pageQuery.build());
        return result;
    }

    private void nullValueProcessing(Page<SysTenantVo> result) {
        List<SysTenantVo> records = result.getRecords();
        for (SysTenantVo record : records) {
            record.setThirdChannelFlag(StringUtils.defaultIfBlank(record.getThirdChannelFlag(), "-"));
            record.setCountry(StringUtils.defaultIfBlank(record.getCountry(), "-"));
            record.setContactUserName(StringUtils.defaultIfBlank(record.getContactUserName(), "-"));
            record.setContactPhone(StringUtils.defaultIfBlank(record.getContactPhone(), "-"));
            record.setContactEmail(StringUtils.defaultIfBlank(record.getContactEmail(), "-"));
            record.setCompanyName(StringUtils.defaultIfBlank(record.getCompanyName(), "-"));
            record.setLicenseNumber(StringUtils.defaultIfBlank(record.getLicenseNumber(), "-"));
            record.setAddress(StringUtils.defaultIfBlank(record.getAddress(), "-"));
            record.setDomain(StringUtils.defaultIfBlank(record.getDomain(), "-"));
            record.setIntro(StringUtils.defaultIfBlank(record.getIntro(), "-"));
            record.setRemark(StringUtils.defaultIfBlank(record.getRemark(), "-"));
            record.setTenantStatusDesc(StringUtils.defaultIfBlank(record.getTenantStatusDesc(), "-"));

            record.setProductSourceType(record.getProductSourceType() != null ? record.getProductSourceType() : "-");

            record.setExtraPerfectionFlag(record.getExtraPerfectionFlag() != null ? record.getExtraPerfectionFlag() : "-");

        }
    }

    /**
     * 查询租户列表（导出用）
     *
     * @param bo
     */
    @Override
    public R<Void> queryPageListForExport(SysTenantBo bo) {
        String fileName = StrUtil.format(FileNameConstants.SYS_TENANT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        Locale headerLocale = ServletUtils.getHeaderLocale();

        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.SysTenant, tempFileSavePath -> {
            Page<SysTenantVo> result = getSysTenantVoPage(bo, new PageQuery());
            List<SysTenantVo> records = result.getRecords();

            List<SysTenantExportVo> exportVoList = new ArrayList<>();
            if (CollUtil.isNotEmpty(records)) {
                for (SysTenantVo tenantVo : records) {
                    String tenantId = tenantVo.getTenantId();
                    SysTenantExportVo exportVo = new SysTenantExportVo();

                    exportVo.setTenantId(tenantId);
                    exportVo.setTenantType(MessageUtils.message(headerLocale, "zsmall.excelFormat.tenantType." + tenantVo.getTenantType()));
                    exportVo.setCreateTime(DateUtil.formatDateTime(tenantVo.getCreateTime()));
                    exportVo.setStatus(MessageUtils.message(headerLocale, "zsmall.excelFormat.tenantStatus." + tenantVo.getStatus()));
                    exportVo.setThirdChannelFlag(tenantVo.getThirdChannelFlag());
                    if (StrUtil.equals(tenantId, TenantConstants.DEFAULT_TENANT_ID)) {
                        exportVoList.add(exportVo);
                        continue;
                    }

                    List<SysUserVo> sysUserVos = userMapper.selectTenantUserByRoleAdmin(tenantId);
                    if (CollUtil.isNotEmpty(sysUserVos)) {
                        SysUserVo sysUserVo = sysUserVos.get(0);
                        String areaCode = sysUserVo.getAreaCode();
                        String phonenumber = sysUserVo.getPhonenumber();

                        if (StrUtil.isNotBlank(phonenumber)) {
                            if (StrUtil.isNotBlank(areaCode)) {
                                phonenumber = StrUtil.builder("(", areaCode, ")", phonenumber).toString();
                            }
                            exportVo.setPhoneNumber(phonenumber);
                        }

                        exportVo.setNickName(sysUserVo.getNickName());
                        exportVo.setEmail(sysUserVo.getEmail());
                        exportVo.setLoginDate(DateUtil.formatDateTime(sysUserVo.getLoginDate()));
                    }
                    exportVoList.add(exportVo);
                }
            }

            File tempFile = new File(tempFileSavePath);
            BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
            ExcelUtil.exportExcelWithLocale(exportVoList, "TenantList", SysTenantExportVo.class, false, outputStream, headerLocale);
            IoUtil.close(outputStream);
            return tempFile;
        });

        return R.ok();
    }

    /**
     * 查询租户列表
     */
    @Override
    public List<SysTenantVo> queryList(SysTenantBo bo) {
        LambdaQueryWrapper<SysTenant> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysTenant> buildQueryWrapper(SysTenantBo bo) {
        LambdaQueryWrapper<SysTenant> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTenantId()), SysTenant::getTenantId, bo.getTenantId());
        lqw.eq(StringUtils.isNotBlank(bo.getTenantType()), SysTenant::getTenantType, bo.getTenantType());
        lqw.like(StringUtils.isNotBlank(bo.getContactUserName()), SysTenant::getContactUserName, bo.getContactUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), SysTenant::getContactPhone, bo.getContactPhone());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), SysTenant::getCompanyName, bo.getCompanyName());
        lqw.eq(StringUtils.isNotBlank(bo.getLicenseNumber()), SysTenant::getLicenseNumber, bo.getLicenseNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), SysTenant::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getIntro()), SysTenant::getIntro, bo.getIntro());
        lqw.like(StringUtils.isNotBlank(bo.getDomain()), SysTenant::getDomain, bo.getDomain());
        lqw.eq(bo.getPackageId() != null, SysTenant::getPackageId, bo.getPackageId());
        lqw.eq(bo.getExpireTime() != null, SysTenant::getExpireTime, bo.getExpireTime());
        lqw.eq(bo.getAccountCount() != null, SysTenant::getAccountCount, bo.getAccountCount());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysTenant::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增租户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(SysTenantBo bo) {
        SysTenant add = MapstructUtils.convert(bo, SysTenant.class);

        // 获取所有租户编号
//        List<String> tenantIds = baseMapper.selectObjs(
//            new LambdaQueryWrapper<SysTenant>().select(SysTenant::getTenantId), Convert::toStr);
        String tenantType = bo.getTenantType();
        // 判断是否非管理员
        checkNotManager(tenantType);
        // 获取非管理员对应默认套餐
        Long packageId = getDefaultPackageId(tenantType);
        if (packageId == null) {
            throw new ServiceException("tenant.package.not.found");
        }

        String tenantId = sysCodeGenerator.codeGenerate(SysConstantEnum.KEY_TENANT_TYPE, tenantType);
        if (StrUtil.isBlank(tenantId)) {
            throw new ServiceException("tenant.number.error");
        }
        add.setTenantId(tenantId);
        boolean flag = baseMapper.insert(add) > 0;
        if (!flag) {
            throw new ServiceException("tenant.number.create.error");
        }
        bo.setId(add.getId());
        bo.setPackageId(packageId);

        // 根据套餐创建角色
        Long roleId = createTenantRole(tenantId, bo.getPackageId());

        // 创建部门: 公司名是部门名称
        SysDept dept = new SysDept();
        dept.setTenantId(tenantId);
        dept.setDeptName(bo.getCompanyName());
        dept.setLeader(bo.getUsername());
        dept.setParentId(Constants.TOP_PARENT_ID);
        dept.setAncestors(Constants.TOP_PARENT_ID.toString());
        deptMapper.insert(dept);
        Long deptId = dept.getDeptId();

        // 角色和部门关联表
        SysRoleDept roleDept = new SysRoleDept();
        roleDept.setRoleId(roleId);
        roleDept.setDeptId(deptId);
        roleDeptMapper.insert(roleDept);

        // 创建系统用户
        SysUser user = new SysUser();
        user.setTenantId(tenantId);
        user.setUserName(bo.getUsername());
        user.setNickName(bo.getUsername());
        user.setPassword(BCrypt.hashpw(bo.getPassword()));
        user.setDeptId(deptId);
        userMapper.insert(user);

        // 用户和角色关联表
        SysUserRole userRole = new SysUserRole();
        userRole.setUserId(user.getUserId());
        userRole.setRoleId(roleId);
        userRoleMapper.insert(userRole);

        // 当前业务默认无租户权限
//        String defaultTenantId = TenantConstants.DEFAULT_TENANT_ID;
//        List<SysDictType> dictTypeList = dictTypeMapper.selectList(
//            new LambdaQueryWrapper<SysDictType>().eq(SysDictType::getTenantId, defaultTenantId));
//        List<SysDictData> dictDataList = dictDataMapper.selectList(
//            new LambdaQueryWrapper<SysDictData>().eq(SysDictData::getTenantId, defaultTenantId));
//        for (SysDictType dictType : dictTypeList) {
//            dictType.setDictId(null);
//            dictType.setTenantId(tenantId);
//        }
//        for (SysDictData dictData : dictDataList) {
//            dictData.setDictCode(null);
//            dictData.setTenantId(tenantId);
//        }
//        dictTypeMapper.insertBatch(dictTypeList);
//        dictDataMapper.insertBatch(dictDataList);

//        List<SysConfig> sysConfigList = configMapper.selectList(
//            new LambdaQueryWrapper<SysConfig>().eq(SysConfig::getTenantId, defaultTenantId));
//        for (SysConfig config : sysConfigList) {
//            config.setConfigId(null);
//            config.setTenantId(tenantId);
//        }
//        configMapper.insertBatch(sysConfigList);
        return true;
    }

    /**
     * 根据租户类型获取默认套餐Id
     *
     * @param tenantType
     * @return
     */
    @Override
    public Long getDefaultPackageId(String tenantType) {
        String key = SysConstant.PREFIX_TENANT_PACKAGE + tenantType.toLowerCase();
        String configValue = iSysConfigService.selectConfigByKey(key);
        if (StrUtil.isNotBlank(configValue)) {
            return Long.parseLong(configValue);
        }
        return null;
    }

    /**
     * 根据租户类型获取默认套餐Id
     *
     * @param tenantType
     * @param productSourceType
     * @return
     */
    @Override
    public Long getDefaultPackageId(String tenantType, String productSourceType) {
        String key = SysConstant.PREFIX_TENANT_PACKAGE + tenantType.toLowerCase() + "." + productSourceType.toLowerCase();
        String configValue = iSysConfigService.selectConfigByKey(key);
        if (StrUtil.isNotBlank(configValue)) {
            return Long.parseLong(configValue);
        }
        return null;
    }

    @Override
    public void checkNotManager(String tenantType) {
        List<String> noManagers = StrUtil.split(SysConstant.KEY_TENANT_NOT_MANAGER, ",");
        boolean isNotManager = noManagers.contains(tenantType);
        if (!isNotManager) {
            throw new ServiceException("tenant.number.error");
        }
    }

    /**
     * 生成租户id
     *
     * @param tenantIds 已有租户id列表
     * @return 租户id
     */
    private String generateTenantId(List<String> tenantIds) {
        // 随机生成6位
        String numbers = RandomUtil.randomNumbers(6);
        // 判断是否存在，如果存在则重新生成
        if (tenantIds.contains(numbers)) {
            generateTenantId(tenantIds);
        }
        return numbers;
    }

    /**
     * 根据租户菜单创建租户角色
     *
     * @param tenantId  租户编号
     * @param packageId 租户套餐id
     * @return 角色id
     */
    @Override
    public Long createTenantRole(String tenantId, Long packageId) {
        // 获取租户套餐
        SysTenantPackage tenantPackage = tenantPackageMapper.selectById(packageId);
        if (ObjectUtil.isNull(tenantPackage)) {
            throw new ServiceException("套餐不存在");
        }
        // 获取套餐菜单id
        List<Long> menuIds = StringUtils.splitTo(tenantPackage.getMenuIds(), Convert::toLong);

        // 创建角色
        SysRole role = new SysRole();
        role.setTenantId(tenantId);
        role.setRoleName(TenantConstants.TENANT_ADMIN_ROLE_NAME);
        role.setRoleKey(TenantConstants.TENANT_ADMIN_ROLE_KEY);
        role.setRoleSort(1);
        role.setStatus(TenantConstants.NORMAL);
        roleMapper.insert(role);
        Long roleId = role.getRoleId();

        // 创建角色菜单
        List<SysRoleMenu> roleMenus = new ArrayList<>(menuIds.size());
        menuIds.forEach(menuId -> {
            SysRoleMenu roleMenu = new SysRoleMenu();
            roleMenu.setRoleId(roleId);
            roleMenu.setMenuId(menuId);
            roleMenus.add(roleMenu);
        });
        roleMenuMapper.insertBatch(roleMenus);

        return roleId;
    }

    /**
     * 获取我的租户信息
     *
     * @return
     */
    @Override
    public SysTenantVo getMyTenant() {
        return null;
    }

    @Override
    public SysTenant getTenantByThirdChannelFlag(String thirdChannelFlag, String tenantId) {
        LambdaQueryWrapper<SysTenant> eq = new LambdaQueryWrapper<SysTenant>().eq(SysTenant::getThirdChannelFlag, thirdChannelFlag)
                                                                              .eq(SysTenant::getTenantId, tenantId)
                                                                              .eq(SysTenant::getDelFlag, 0);
        return TenantHelper.ignore(()->baseMapper.selectOne(eq));
    }

    @Override
    public Map<String, SysTenant> getTenantMapByTenantIds(List<String> tenantIds) {
        if (CollUtil.isNotEmpty(tenantIds)) {
            List<SysTenant> sysTenantList = TenantHelper.ignore(() -> baseMapper.selectList(new LambdaQueryWrapper<SysTenant>().in(SysTenant::getTenantId, tenantIds)));
            if (CollUtil.isNotEmpty(sysTenantList)) {
                return sysTenantList.stream().collect(Collectors.toMap(SysTenant::getTenantId, item -> item));
            }
        }
        return null;
    }

    /**
     * 修改租户
     */
    @CacheEvict(cacheNames = CacheNames.SYS_TENANT, key = "#bo.tenantId")
    @Override
    public Boolean updateByBo(SysTenantBo bo) {
        SysTenant tenant = MapstructUtils.convert(bo, SysTenant.class);
        tenant.setTenantId(null);
        tenant.setPackageId(null);
        return baseMapper.updateById(tenant) > 0;
    }


    /**
     * 修改租户
     */
    @CacheEvict(cacheNames = CacheNames.SYS_TENANT, key = "#vo.tenantId")
    @Override
    public Boolean updateByVo(SysTenantVo vo) {
        String tenantId = vo.getTenantId();
        SysTenant tenant = MapstructUtils.convert(vo, SysTenant.class);
        tenant.setTenantId(null);
        tenant.setPackageId(null);
        return baseMapper.update(tenant, new LambdaUpdateWrapper<SysTenant>().eq(SysTenant::getTenantId, tenantId))> 0;
    }

    /**
     * 修改租户状态
     *
     * @param bo 租户信息
     * @return 结果
     */
    @CacheEvict(cacheNames = CacheNames.SYS_TENANT, key = "#bo.tenantId")
    @Override
    public int updateTenantStatus(SysTenantBo bo) {
        SysTenant tenant = MapstructUtils.convert(bo, SysTenant.class);
        int flag = baseMapper.updateById(tenant);
        if(flag > 0 && StrUtil.equals(tenant.getStatus(), "1")) {
            tenantLogout(tenant);
        }

        return flag;
    }

    /**
     * 租户下所有用户强制登出
     * @param tenant
     */
    private void tenantLogout(SysTenant tenant) {
        // 查找租户下所有可用的用户，全部强制下线
        LambdaQueryWrapper<SysUser> userLwq = new LambdaQueryWrapper<SysUser>()
            .eq(TenantEntity::getTenantId, tenant.getTenantId());
        List<SysUser> sysUsers = TenantHelper.ignore(() -> userMapper.selectList(userLwq));
        if(CollUtil.isNotEmpty(sysUsers)) {
            sysUsers.forEach(user -> {
                String userType = user.getUserType();
                Long userId = user.getUserId();
                String loginId = userType + ":" + userId;

                StpUtil.logout(loginId);
            });
        }
    }

    /**
     * 校验租户是否允许操作
     *
     * @param tenantId 租户ID
     */
    @Override
    public void checkTenantAllowed(String tenantId) {
        if (ObjectUtil.isNotNull(tenantId) && TenantConstants.DEFAULT_TENANT_ID.equals(tenantId)) {
            throw new ServiceException("不允许操作管理租户");
        }
    }

    /**
     * 批量删除租户
     */
    @CacheEvict(cacheNames = CacheNames.SYS_TENANT, allEntries = true)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
            if (ids.contains(TenantConstants.SUPER_ADMIN_ID)) {
                throw new ServiceException("超管租户不能删除");
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 校验企业名称是否唯一
     */
    @Override
    public boolean checkCompanyNameUnique(SysTenantBo bo) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysTenant>()
            .eq(SysTenant::getCompanyName, bo.getCompanyName())
            .ne(ObjectUtil.isNotNull(bo.getTenantId()), SysTenant::getTenantId, bo.getTenantId()));
        return !exist;
    }

    /**
     * 校验账号可用用户数量
     */
    @Override
    public boolean checkAccountBalance(String tenantId) {
        SysTenantVo tenant = SpringUtils.getAopProxy(this).queryByTenantId(tenantId);
        // 如果用户数量为-1代表不限制
        if (tenant.getAccountCount() == -1) {
            return true;
        }
        Long userNumber = userMapper.selectCount(new LambdaQueryWrapper<>());
        // 如果用户数量大于0代表还有可用名额
        return tenant.getAccountCount() - userNumber > 0;
    }

    /**
     * 校验有效期
     */
    @Override
    public boolean checkExpireTime(String tenantId) {
        SysTenantVo tenant = SpringUtils.getAopProxy(this).queryByTenantId(tenantId);
        // 如果未设置过期时间代表不限制
        if (ObjectUtil.isNull(tenant.getExpireTime())) {
            return true;
        }
        // 如果当前时间在过期时间之前则通过
        return new Date().before(tenant.getExpireTime());
    }

    /**
     * 同步租户套餐
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncTenantPackage(String tenantId, String packageId) {
        SysTenantPackage tenantPackage = tenantPackageMapper.selectById(packageId);
        List<SysRole> roles = roleMapper.selectList(
            new LambdaQueryWrapper<SysRole>().eq(SysRole::getTenantId, tenantId));
        List<Long> roleIds = new ArrayList<>(roles.size() - 1);
        List<Long> menuIds = StringUtils.splitTo(tenantPackage.getMenuIds(), Convert::toLong);
        roles.forEach(item -> {
            if (TenantConstants.TENANT_ADMIN_ROLE_KEY.equals(item.getRoleKey())) {
                List<SysRoleMenu> roleMenus = new ArrayList<>(menuIds.size());
                menuIds.forEach(menuId -> {
                    SysRoleMenu roleMenu = new SysRoleMenu();
                    roleMenu.setRoleId(item.getRoleId());
                    roleMenu.setMenuId(menuId);
                    roleMenus.add(roleMenu);
                });
                roleMenuMapper.delete(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getRoleId, item.getRoleId()));
                roleMenuMapper.insertBatch(roleMenus);
            } else {
                roleIds.add(item.getRoleId());
            }
        });
        if (!roleIds.isEmpty()) {
            roleMenuMapper.delete(
                new LambdaQueryWrapper<SysRoleMenu>().in(SysRoleMenu::getRoleId, roleIds).notIn(!menuIds.isEmpty(), SysRoleMenu::getMenuId, menuIds));
        }
        return true;
    }

    /**
     * 是否存在租户Id
     *
     * @param tenantId
     * @return {Boolean} true-存在，false-不存在
     */
    @Override
    public Boolean existTenantId(String tenantId) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysTenant>()
            .eq(SysTenant::getTenantId, tenantId));
        return exist;
    }

    @Override
    public List<ApprovedTenantVo> getApprovedTenant(String tenantId, String nickName,Integer isCalculation) {
        List<ApprovedTenantVo> ignore = TenantHelper.ignore(() -> baseMapper.getApprovedTenant(tenantId, nickName, isCalculation));
        Set<String> tenantIds = ignore.stream()
                                    .map(ApprovedTenantVo::getTenantId) // 假设这是获取tenantId的方法
                                    .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(tenantIds)){
            LambdaQueryWrapper<SysUser> user = new LambdaQueryWrapper<>();
            user.in(SysUser::getTenantId,tenantIds);
            List<SysUser> userList = TenantHelper.ignore(() -> userMapper.selectList(user));
            Map<String, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getTenantId, item -> item));
            ignore.forEach(s->{
                SysUser sysUser = userMap.get(s.getTenantId());
                if (ObjectUtil.isNotNull(sysUser)){
                    s.setNickName(sysUser.getNickName());
                }
            });
        }
        return  ignore;
    }

    @Override
    public void updateTenantIsCalculation(List<String> tenantIds, Integer isCalculation) {
        baseMapper.updateTenantIsCalculation(tenantIds,isCalculation);
    }

    @Override
    public Boolean getIsApprovedTenant(String tenantId, Integer isCalculation) {
        String ignore = TenantHelper.ignore(() -> baseMapper.getIsApprovedTenant(tenantId, isCalculation));
        return !ObjectUtil.isNull(ignore);
    }

    @Override
    public Map<String,Map<String,String>> queryByTenantIds(List<String> tenantIds) {
        Map<String,Map<String,String>> map =null;
        map = TenantHelper.ignore(()->baseMapper.queryByTenantIds(tenantIds));
        return map;
    }

    @Override
    public List<String> listTenantIdByTenantType(String tenantType) {
        LambdaQueryWrapper<SysTenant> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysTenant::getTenantType,tenantType);
        List<SysTenant> sysTenants = TenantHelper.ignore(() -> baseMapper.selectList(lqw));
        if (CollUtil.isNotEmpty(sysTenants)){
            return sysTenants.stream().map(SysTenant::getTenantId).distinct().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<SysTenantVo> getSupTenants() {
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<SysTenant> sysTenantVoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysTenantVoLambdaQueryWrapper.eq(SysTenant::getTenantType, TenantType.Supplier.name());
        sysTenantVoLambdaQueryWrapper.eq(SysTenant::getDelFlag, 0);
        sysTenantVoLambdaQueryWrapper.eq(SysTenant::getStatus, 0);
        List<SysTenantVo> ignore = TenantHelper.ignore(() -> baseMapper.selectVoList(sysTenantVoLambdaQueryWrapper));
        for (SysTenantVo sysTenantVo : ignore) {
            if(ObjectUtil.isNotEmpty(sysTenantVo) && ObjectUtil.isEmpty(sysTenantVo.getCompanyName())){
                sysTenantVo.setCompanyName("-");
            }

        }
        List<SysTenantVo> collect = ignore.stream().filter(i->!i.getTenantId().equals(tenantId)).collect(Collectors.toList());
        return collect;
    }

    @Override
    public void tenantManagement(SysTenantBo bo, PageQuery pageQuery, HttpServletResponse response) {
        String fileName = StrUtil.format(FileNameConstants.TENANT_MANAGEMENT_EXPORT, DateUtil.format(new Date(), "yyMMdd-HHmmssSS"));
        Locale headerLocale = ServletUtils.getHeaderLocale();
        DownloadRecordUtil.generate(fileName, DownloadTypePlusEnum.TenantManagementExport, tempFileSavePath -> {
            try {
                TimeInterval timer = DateUtil.timer();
                File tempFile = new File(tempFileSavePath);
                BufferedOutputStream outputStream = FileUtil.getOutputStream(tempFile);
                Page<SysTenantVo> sysTenantVoPage = writeTenantManagementExportV2(bo, pageQuery, bo.getTenantId());
                List<SysTenantVo> records = sysTenantVoPage.getRecords();
                com.hengjian.common.excel.utils.ExcelUtil.exportExcelWithLocale(records, "Tenant Management", SysTenantVo.class, false, outputStream, headerLocale);
                IoUtil.close(outputStream);
                Console.log("[租户管理导出]耗时: {} ms", timer.intervalMs());
                return tempFile;
            } catch (Exception e) {
                log.error(StrUtil.format(StrUtil.format("[租户管理导出],处理数据异常，原因：", e)));
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public void checkTenantAllowedBatch(List<SysTenantBo> bos) {
        for (SysTenantBo bo : bos) {
            checkTenantAllowed(bo.getTenantId());
        }
    }

    @Override
    public int updateTenantStatusBatch(List<SysTenantBo> bos) {
        for (SysTenantBo bo : bos) {
            SysTenant tenant = MapstructUtils.convert(bo, SysTenant.class);
            int flag = baseMapper.updateById(tenant);
            if(flag > 0 && StrUtil.equals(tenant.getStatus(), "1")) {
                tenantLogout(tenant);
            }
        }
        return 1;
    }

    private Page<SysTenantVo> writeTenantManagementExportV2(SysTenantBo bo, PageQuery pageQuery,
                                                                         String tenantId) {
        return getSysTenantVoPage(bo,pageQuery);
    }
}
